import { createTheme, type ThemeOptions } from '@mui/material/styles';

// Material Design 3 Color Tokens
const colorTokens = {
  // Primary colors
  primary: {
    main: '#6750A4',
    light: '#EADDFF',
    dark: '#21005D',
    contrastText: '#FFFFFF',
  },
  // Secondary colors
  secondary: {
    main: '#625B71',
    light: '#E8DEF8',
    dark: '#1D192B',
    contrastText: '#FFFFFF',
  },
  // Tertiary colors
  tertiary: {
    main: '#7D5260',
    light: '#FFD8E4',
    dark: '#31111D',
    contrastText: '#FFFFFF',
  },
  // Error colors
  error: {
    main: '#BA1A1A',
    light: '#FFDAD6',
    dark: '#410002',
    contrastText: '#FFFFFF',
  },
  // Surface colors
  surface: {
    main: '#FFFBFE',
    variant: '#E7E0EC',
    container: '#F3EDF7',
    containerHigh: '#ECE6F0',
    containerHighest: '#E6E0E9',
  },
  // On-surface colors
  onSurface: {
    main: '#1C1B1F',
    variant: '#49454F',
  },
  // Background
  background: {
    default: '#FFFBFE',
    paper: '#FFFBFE',
  },
};

// Typography scale following Material Design 3
const typography = {
  fontFamily: '"Roboto", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
  // Display styles
  h1: {
    fontSize: '3.5rem',
    fontWeight: 400,
    lineHeight: 1.17,
    letterSpacing: '-0.25px',
  },
  h2: {
    fontSize: '2.8rem',
    fontWeight: 400,
    lineHeight: 1.2,
    letterSpacing: '0px',
  },
  h3: {
    fontSize: '2.25rem',
    fontWeight: 400,
    lineHeight: 1.22,
    letterSpacing: '0px',
  },
  h4: {
    fontSize: '1.75rem',
    fontWeight: 400,
    lineHeight: 1.29,
    letterSpacing: '0.25px',
  },
  h5: {
    fontSize: '1.5rem',
    fontWeight: 400,
    lineHeight: 1.33,
    letterSpacing: '0px',
  },
  h6: {
    fontSize: '1.25rem',
    fontWeight: 500,
    lineHeight: 1.4,
    letterSpacing: '0.15px',
  },
  // Body styles
  body1: {
    fontSize: '1rem',
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: '0.5px',
  },
  body2: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.43,
    letterSpacing: '0.25px',
  },
  // Label styles
  button: {
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: 1.43,
    letterSpacing: '1.25px',
  },
  caption: {
    fontSize: '0.75rem',
    fontWeight: 400,
    lineHeight: 1.33,
    letterSpacing: '0.4px',
  },
  overline: {
    fontSize: '0.625rem',
    fontWeight: 500,
    lineHeight: 1.6,
    letterSpacing: '1.5px',
  },
};

// Component customizations following Material Design 3
const components = {
  MuiCssBaseline: {
    styleOverrides: {
      body: {
        backgroundColor: colorTokens.background.default,
        color: colorTokens.onSurface.main,
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: '12px',
        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
        backgroundColor: colorTokens.surface.container,
      },
    },
  },
  MuiButton: {
    styleOverrides: {
      root: {
        borderRadius: '20px',
        fontWeight: 500,
        padding: '10px 24px',
      },
      contained: {
        boxShadow: 'none',
        '&:hover': {
          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
        },
      },
    },
  },
  MuiChip: {
    styleOverrides: {
      root: {
        borderRadius: '8px',
        height: '32px',
      },
    },
  },
  MuiAppBar: {
    styleOverrides: {
      root: {
        backgroundColor: colorTokens.surface.container,
        color: colorTokens.onSurface.main,
        boxShadow: 'none',
        borderBottom: `1px solid ${colorTokens.surface.variant}`,
      },
    },
  },
};

const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: colorTokens.primary,
    secondary: colorTokens.secondary,
    error: colorTokens.error,
    background: colorTokens.background,
    text: {
      primary: colorTokens.onSurface.main,
      secondary: colorTokens.onSurface.variant,
    },
  },
  typography,
  components,
  shape: {
    borderRadius: 12,
  },
  spacing: 8,
};

export const theme = createTheme(themeOptions);
export default theme;
