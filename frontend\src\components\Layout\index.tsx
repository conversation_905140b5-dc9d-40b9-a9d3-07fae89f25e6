import { Outlet } from 'react-router-dom';
import { Box, Container, CssBaseline } from '@mui/material';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { ChatAssistant } from '@/components/ChatAssistant';

export const Layout = () => {
  return (
    <>
      <CssBaseline />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <Header />
        
        <Container
          component="main"
          maxWidth="lg"
          sx={{
            flex: 1,
            py: 4,
            px: { xs: 2, sm: 3 },
          }}
        >
          <Outlet />
        </Container>
        
        <Footer />
        
        {/* Floating Chat Assistant */}
        <ChatAssistant />
      </Box>
    </>
  );
};
