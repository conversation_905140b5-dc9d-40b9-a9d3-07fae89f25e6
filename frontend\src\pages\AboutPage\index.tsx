import {
  Box,
  Typography,
  Paper,
  Avatar,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  Code,
  Web,
  Storage,
  Psychology,
  School,
} from '@mui/icons-material';

export const AboutPage = () => {
  const skills = [
    { category: '前端开发', items: ['React', 'TypeScript', 'Vue.js', 'Material-UI', 'Tailwind CSS'] },
    { category: '后端开发', items: ['Rust', 'Node.js', 'Python', 'Go', 'Java'] },
    { category: '数据库', items: ['PostgreSQL', 'MySQL', 'SQLite', 'MongoDB', 'Redis'] },
    { category: '云服务', items: ['AWS', 'Docker', 'Kubernetes', 'Vercel', 'Netlify'] },
  ];

  const interests = [
    { icon: <Code />, title: '编程技术', description: '热爱探索新的编程语言和技术框架' },
    { icon: <Web />, title: 'Web开发', description: '专注于现代化的Web应用开发' },
    { icon: <Storage />, title: '系统架构', description: '关注高性能、可扩展的系统设计' },
    { icon: <Psychology />, title: '人工智能', description: '对AI和机器学习技术充满兴趣' },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 3, md: 6 },
          mb: 4,
          backgroundColor: 'surface.container',
          borderRadius: 3,
          textAlign: 'center',
        }}
      >
        <Avatar
          sx={{
            width: 120,
            height: 120,
            mx: 'auto',
            mb: 3,
            backgroundColor: 'primary.main',
            fontSize: '3rem',
          }}
        >
          C
        </Avatar>
        
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 700,
            mb: 2,
          }}
        >
          关于 Cyrus
        </Typography>
        
        <Typography
          variant="h6"
          color="text.secondary"
          sx={{
            maxWidth: 600,
            mx: 'auto',
            lineHeight: 1.6,
          }}
        >
          一名热爱技术的全栈开发者，专注于现代化Web应用开发和系统架构设计。
          喜欢分享技术见解，探索新的编程范式和最佳实践。
        </Typography>
      </Paper>

      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '2fr 1fr' }, gap: 4 }}>
        {/* About Me */}
        <Box>
          <Paper
            elevation={0}
            sx={{
              p: 4,
              backgroundColor: 'background.paper',
              borderRadius: 3,
              mb: 4,
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
              个人简介
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
              我是一名充满热情的软件开发者，拥有多年的全栈开发经验。我相信技术的力量可以改变世界，
              并致力于创建高质量、用户友好的软件解决方案。
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
              在我的职业生涯中，我参与了从小型创业公司到大型企业的各种项目。我特别擅长现代化的Web技术栈，
              包括React、TypeScript、Rust等。我也对系统架构设计、性能优化和用户体验设计有着深入的理解。
            </Typography>
            
            <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
              除了编程，我还热衷于技术写作和知识分享。我相信通过分享经验和见解，
              我们可以共同推动技术社区的发展，帮助更多的开发者成长。
            </Typography>
          </Paper>

          {/* Skills */}
          <Paper
            elevation={0}
            sx={{
              p: 4,
              backgroundColor: 'background.paper',
              borderRadius: 3,
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
              技能专长
            </Typography>
            
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' }, gap: 3 }}>
              {skills.map((skillGroup) => (
                <Box key={skillGroup.category}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    {skillGroup.category}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {skillGroup.items.map((skill) => (
                      <Chip
                        key={skill}
                        label={skill}
                        variant="outlined"
                        sx={{
                          '&:hover': {
                            backgroundColor: 'primary.light',
                          },
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
          </Paper>
        </Box>

        {/* Sidebar */}
        <Box>
          {/* Interests */}
          <Paper
            elevation={0}
            sx={{
              p: 3,
              backgroundColor: 'background.paper',
              borderRadius: 3,
              mb: 4,
            }}
          >
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
              兴趣领域
            </Typography>
            
            <List disablePadding>
              {interests.map((interest, index) => (
                <ListItem key={index} disablePadding sx={{ mb: 2 }}>
                  <Card
                    elevation={0}
                    sx={{
                      width: '100%',
                      backgroundColor: 'surface.container',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        <Box sx={{ color: 'primary.main', mt: 0.5 }}>
                          {interest.icon}
                        </Box>
                        <Box>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
                            {interest.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {interest.description}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </ListItem>
              ))}
            </List>
          </Paper>

          {/* Contact Info */}
          <Paper
            elevation={0}
            sx={{
              p: 3,
              backgroundColor: 'background.paper',
              borderRadius: 3,
            }}
          >
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
              联系方式
            </Typography>
            
            <List disablePadding>
              <ListItem disablePadding>
                <ListItemIcon>
                  <School color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="博客"
                  secondary="分享技术文章和经验"
                />
              </ListItem>
            </List>
            
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              欢迎通过博客评论或邮件与我交流技术话题！
            </Typography>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};
