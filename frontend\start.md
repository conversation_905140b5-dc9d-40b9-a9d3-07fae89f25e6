# 🚀 Cyrus Blog Frontend 快速启动指南

## 📋 前置条件

确保您的系统已安装：
- Node.js >= 18.0.0
- npm >= 8.0.0

## 🔧 启动步骤

### 1. 进入前端目录
```bash
cd frontend
```

### 2. 安装依赖（如果还没有安装）
```bash
npm install
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，确保 API 地址正确
# VITE_API_BASE_URL=http://127.0.0.1:3001
```

### 4. 启动开发服务器
```bash
npm run dev
```

### 5. 打开浏览器
访问 http://localhost:5173

## 🎯 功能验证

### 前端功能测试
- ✅ 首页文章列表展示
- ✅ 搜索功能
- ✅ 文章详情页面
- ✅ 分类页面
- ✅ 关于页面
- ✅ 响应式设计
- ✅ AI 聊天助手

### 后端连接测试
确保后端服务正在运行：
```bash
# 在另一个终端中启动后端
cd ../backend
cargo run
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 如果 5173 端口被占用，Vite 会自动选择其他端口
   # 或者手动指定端口
   npm run dev -- --port 3000
   ```

2. **API 连接失败**
   - 检查后端服务是否启动 (http://127.0.0.1:3001)
   - 检查 .env 文件中的 API 地址配置
   - 查看浏览器控制台的网络请求

3. **依赖安装问题**
   ```bash
   # 清除 node_modules 重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📦 构建生产版本

```bash
# 构建
npm run build

# 预览构建结果
npm run preview
```

## 🎨 自定义配置

### 修改主题色彩
编辑 `src/theme/index.ts` 文件中的色彩配置

### 修改 API 地址
编辑 `.env` 文件中的 `VITE_API_BASE_URL`

### 添加新页面
1. 在 `src/pages/` 创建新组件
2. 在 `src/router/index.tsx` 添加路由

---

**🎉 享受您的现代化博客前端！**
