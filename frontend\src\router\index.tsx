import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { SimpleHomePage } from '@/pages/HomePage/SimpleHomePage';
import { PostPage } from '@/pages/PostPage';
import { CategoryPage } from '@/pages/CategoryPage';
import { AboutPage } from '@/pages/AboutPage';
import { NotFoundPage } from '@/pages/NotFoundPage';

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <SimpleHomePage />,
      },
      {
        path: 'posts/:slug',
        element: <PostPage />,
      },
      {
        path: 'categories/:category',
        element: <CategoryPage />,
      },
      {
        path: 'about',
        element: <AboutPage />,
      },
      {
        path: '*',
        element: <NotFoundPage />,
      },
    ],
  },
]);

export const AppRouter = () => <RouterProvider router={router} />;
export default AppRouter;
