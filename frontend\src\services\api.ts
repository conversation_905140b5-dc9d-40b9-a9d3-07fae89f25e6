import axios from 'axios';

// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:3001';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('adminToken');
    if (token && config.url?.includes('/admin/')) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('adminToken');
      window.location.href = '/admin/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  content?: string;
  slug: string;
  date: string;
  categories: string[];
  created_at: string;
  updated_at: string;
}

export interface BlogPostsResponse {
  success: boolean;
  data: {
    posts: BlogPost[];
    totalPosts: number;
    totalPages: number;
  };
}

export interface BlogPostResponse {
  success: boolean;
  data: BlogPost;
}

export interface CategoriesResponse {
  success: boolean;
  data: string[];
}

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
}

export interface ChatRequest {
  message: string;
  conversationHistory: ChatMessage[];
}

export interface ChatResponse {
  success: boolean;
  data: {
    response: string;
  };
}

export interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
}

// API Functions
export const blogApi = {
  // Public Blog APIs
  getPosts: async (params?: { page?: number; limit?: number; q?: string }): Promise<BlogPostsResponse> => {
    const response = await api.get('/api/posts', { params });
    return response.data;
  },

  getPostBySlug: async (slug: string): Promise<BlogPostResponse> => {
    const response = await api.get(`/api/posts/${slug}`);
    return response.data;
  },

  getCategories: async (): Promise<CategoriesResponse> => {
    const response = await api.get('/api/categories');
    return response.data;
  },

  getPostsByCategory: async (category: string): Promise<BlogPostsResponse> => {
    const response = await api.get(`/api/categories/${category}`);
    return response.data;
  },

  // Chat API
  chat: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post('/api/chat', request);
    return response.data;
  },

  // Health Check
  healthCheck: async (): Promise<HealthResponse> => {
    const response = await api.get('/api/health');
    return response.data;
  },
};

export default api;
