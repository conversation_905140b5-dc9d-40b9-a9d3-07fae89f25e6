import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  Alert,
  Skeleton,
  Breadcrumbs,
} from '@mui/material';
import {
  CalendarToday,
  Category,
  ArrowBack,
  Share,
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { blogApi, type BlogPost } from '@/services/api';
import 'highlight.js/styles/github.css';

export const PostPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) {
        setError('文章不存在');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const response = await blogApi.getPostBySlug(slug);
        
        if (response.success) {
          setPost(response.data);
        } else {
          setError('文章不存在');
        }
      } catch (err) {
        setError('加载文章失败，请稍后重试');
        console.error('Error fetching post:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN });
    } catch {
      return dateString;
    }
  };

  const handleShare = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Share cancelled or failed');
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  if (loading) {
    return (
      <Box>
        <Skeleton variant="text" width={200} height={40} sx={{ mb: 2 }} />
        <Skeleton variant="text" width="100%" height={60} sx={{ mb: 2 }} />
        <Skeleton variant="text" width={300} height={30} sx={{ mb: 4 }} />
        <Skeleton variant="rectangular" width="100%" height={400} />
      </Box>
    );
  }

  if (error || !post) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || '文章不存在'}
        </Alert>
        <Button
          component={Link}
          to="/"
          startIcon={<ArrowBack />}
          variant="outlined"
        >
          返回首页
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
          首页
        </Link>
        <Typography color="text.primary">文章详情</Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        component={Link}
        to="/"
        startIcon={<ArrowBack />}
        sx={{ mb: 3 }}
        variant="text"
      >
        返回文章列表
      </Button>

      {/* Article Header */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 3, md: 4 },
          mb: 4,
          backgroundColor: 'surface.container',
          borderRadius: 3,
        }}
      >
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 700,
            mb: 3,
            lineHeight: 1.2,
          }}
        >
          {post.title}
        </Typography>

        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            justifyContent: 'space-between',
            gap: 2,
            mb: 3,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <CalendarToday sx={{ fontSize: 18, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {formatDate(post.date)}
              </Typography>
            </Box>
          </Box>

          <Button
            startIcon={<Share />}
            onClick={handleShare}
            variant="outlined"
            size="small"
          >
            分享
          </Button>
        </Box>

        {/* Categories */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {post.categories.map((category) => (
            <Chip
              key={category}
              label={category}
              component={Link}
              to={`/categories/${category}`}
              clickable
              icon={<Category sx={{ fontSize: 16 }} />}
              sx={{
                '&:hover': {
                  backgroundColor: 'primary.light',
                },
              }}
            />
          ))}
        </Box>
      </Paper>

      {/* Article Content */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 3, md: 4 },
          backgroundColor: 'background.paper',
          borderRadius: 3,
          '& .markdown-content': {
            lineHeight: 1.8,
            '& h1, & h2, & h3, & h4, & h5, & h6': {
              fontWeight: 600,
              mt: 3,
              mb: 2,
              color: 'text.primary',
            },
            '& h1': { fontSize: '2rem' },
            '& h2': { fontSize: '1.75rem' },
            '& h3': { fontSize: '1.5rem' },
            '& h4': { fontSize: '1.25rem' },
            '& p': {
              mb: 2,
              color: 'text.primary',
            },
            '& ul, & ol': {
              mb: 2,
              pl: 3,
            },
            '& li': {
              mb: 0.5,
            },
            '& blockquote': {
              borderLeft: 4,
              borderColor: 'primary.main',
              pl: 2,
              py: 1,
              my: 2,
              backgroundColor: 'grey.50',
              fontStyle: 'italic',
            },
            '& code': {
              backgroundColor: 'grey.100',
              padding: '2px 6px',
              borderRadius: 1,
              fontSize: '0.875rem',
              fontFamily: 'monospace',
            },
            '& pre': {
              backgroundColor: 'grey.100',
              p: 2,
              borderRadius: 2,
              overflow: 'auto',
              mb: 2,
              '& code': {
                backgroundColor: 'transparent',
                padding: 0,
              },
            },
            '& img': {
              maxWidth: '100%',
              height: 'auto',
              borderRadius: 2,
              my: 2,
            },
            '& table': {
              width: '100%',
              borderCollapse: 'collapse',
              mb: 2,
            },
            '& th, & td': {
              border: 1,
              borderColor: 'divider',
              p: 1,
              textAlign: 'left',
            },
            '& th': {
              backgroundColor: 'grey.100',
              fontWeight: 600,
            },
          },
        }}
      >
        <Box className="markdown-content">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight]}
          >
            {post.content || post.excerpt}
          </ReactMarkdown>
        </Box>
      </Paper>
    </Box>
  );
};
